<script setup lang="ts">
import Container from './components/Container.vue'
import Navbar from './components/Navbar.vue'
import Particles from './components/Particles.vue'
import ResponsiveIndicator from './components/ResponsiveIndicator.vue'

// All content (projects, articles, and abouts) is now loaded directly from MDX files
// using import.meta.glob in utils/content.ts
</script>

<template>
  <div class="grid h-dvh place-items-center bg-[#3D3D3D] font-mono overflow-hidden">
    <Container>
      <section class="relative flex-1 overflow-y-auto px-2 md:px-3 lg:px-4">
        <router-view />
      </section>
      <Navbar />
    </Container>
    <Particles />
    <ResponsiveIndicator />
    <div class="fixed h-[300%] w-[300%] bg-grain-noise opacity-5 animate-grain pointer-events-none top-0" aria-hidden="true" />
    <div class="bg-grid-pattern absolute left-0 top-0 h-full w-full" />
  </div>
</template>
